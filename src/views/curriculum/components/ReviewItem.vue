<template>
  <div class="review-item" :class="[`status-${review.status}`, { 'is-overdue': isOverdue }]">
    <!-- 复习基本信息 -->
    <div class="review-header">
      <div class="review-info">
        <div class="student-name">
          <el-icon class="student-icon"><User /></el-icon>
          {{ review.studentName }}
        </div>
        <!-- <div class="review-meta">
          <span class="review-type">{{ reviewTypeText }}</span>
          <span class="word-count" v-if="review.wordCount">{{ review.wordCount }}个单词</span>
        </div> -->
      </div>
      <div class="review-status">
        <el-tag 
          :type="statusTagType" 
          size="small"
          :effect="review.status === '进行中' ? 'dark' : 'light'"
        >
          <el-icon class="status-icon">
            <component :is="statusIcon" />
          </el-icon>
          {{ statusText }}
        </el-tag>
      </div>
    </div>

    <!-- 复习时间信息 -->
    <div class="review-time">
      <el-icon class="time-icon"><Clock /></el-icon>
      <span class="scheduled-time">{{ formatDateTime(getReviewDateTime()) }}</span>
      <span v-if="isOverdue" class="overdue-text">已逾期</span>
    </div>

    <!-- 复习操作按钮 -->
    <div class="review-actions" v-if="showActions">
      <el-button 
        v-if="review.status === '待开始'" 
        type="primary" 
        size="small"
        :icon="VideoPlay"
        @click="handleStartReview"
        :loading="loading.start"
      >
        去复习
      </el-button>

      <el-button 
        v-if="review.status === '进行中'" 
        type="info" 
        size="small"
        :icon="VideoPlay"
        @click="handleStartReview"
        :loading="loading.complete"
      >
        进入复习
      </el-button>

      <el-button 
        v-if="review.status === '已完成'" 
        type="success" 
        size="small"
        :icon="View"
        @click="handleStartReview"
        plain
      >
        查看详情
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import {
  User,
  Clock,
  VideoPlay,
  View,
  CircleCheck
} from '@element-plus/icons-vue'
import { useCurriculumStore } from '@/stores/curriculum'

const props = defineProps({
  review: {
    type: Object,
    required: true
  },
  showActions: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['reviewStart', 'reviewComplete', 'reviewView'])

const curriculumStore = useCurriculumStore()

// 加载状态
const loading = ref({
  start: false,
  complete: false
})

// 获取复习课时间 - 修复：支持统一数据结构
const getReviewDateTime = () => {
  console.log('🔧 [修复] ReviewItem getReviewDateTime:', {
    scheduledDate: props.review.scheduledDate,
    startTime: props.review.startTime,
    type: props.review.type
  })
  
  // 优先使用scheduledDate，其次使用startTime
  return props.review.scheduledDate || props.review.startTime || new Date().toISOString()
}

// 复习类型文本 - 修复：支持统一数据结构（暂时注释，未来可能使用）
// const reviewTypeText = computed(() => {
//   const typeMap = {
//     daily: '每日复习',
//     weekly: '周复习',
//     monthly: '月复习'
//   }
//
//   // 优先使用reviewType字段，其次根据type字段判断
//   if (props.review.reviewType) {
//     return typeMap[props.review.reviewType] || '复习'
//   } else if (props.review.type === 'review') {
//     return '复习课'
//   }
//   return '复习'
// })

// 状态标签类型
const statusTagType = computed(() => {
  const typeMap = {
    待开始: 'warning',
    进行中: 'primary',
    已完成: 'success'
  }
  return typeMap[props.review.status] || 'info'
})

// 状态文本
const statusText = computed(() => {
  const textMap = {
    待开始: '待复习',
    进行中: '复习中',
    已完成: '已完成'
  }
  return textMap[props.review.status] || '未知'
})

// 状态图标
const statusIcon = computed(() => {
  const iconMap = {
    待开始: Clock,
    进行中: VideoPlay,
    已完成: CircleCheck
  }
  return iconMap[props.review.status] || Clock
})

// 是否逾期
const isOverdue = computed(() => {
  if (props.review.status !== '待开始') return false

  const now = new Date()
  const reviewDateTime = getReviewDateTime()
  const scheduledDate = new Date(reviewDateTime)
  
  console.log('🔧 [修复] ReviewItem isOverdue:', {
    now: now.toISOString(),
    reviewDateTime,
    scheduledDate: scheduledDate.toISOString(),
    isOverdue: now > scheduledDate
  })
  
  return now > scheduledDate
})

// 进度相关（暂时注释，未来可能使用）
// const progressPercentage = computed(() => {
//   if (props.review.status !== '进行中') return 0
//   return Math.floor(Math.random() * 80) + 20
// })

// const progressStatus = computed(() => {
//   if (progressPercentage.value >= 80) return 'success'
//   if (progressPercentage.value >= 50) return 'warning'
//   return 'exception'
// })

// 格式化日期时间
const formatDateTime = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
  
  const diffDays = Math.floor((targetDate - today) / (1000 * 60 * 60 * 24))
  
  if (diffDays === 0) {
    return `今天`
  } else if (diffDays === 1) {
    return `明天`
  } else if (diffDays === -1) {
    return `昨天`
  } else if (diffDays > 1 && diffDays <= 7) {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    return `${weekdays[date.getDay()]}`
  } else {
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit'
    })
  }
}

// 备用格式化函数（暂时注释）
// const formatDateTime1 = (dateString) => {
//   const date = new Date(dateString)
//   const now = new Date()
//   const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
//   const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
//
//   const diffDays = Math.floor((targetDate - today) / (1000 * 60 * 60 * 24))
//
//   if (diffDays === 0) {
//     return `今天 ${date.toLocaleString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`
//   } else if (diffDays === 1) {
//     return `明天 ${date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`
//   } else if (diffDays === -1) {
//     return `昨天 ${date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`
//   } else if (diffDays > 1 && diffDays <= 7) {
//     const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
//     return `${weekdays[date.getDay()]} ${date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`
//   } else {
//     return date.toLocaleString('zh-CN', {
//       month: '2-digit',
//       day: '2-digit',
//       hour: '2-digit',
//       minute: '2-digit'
//     })
//   }
// }

// 处理开始复习
const handleStartReview = async () => {
//   loading.value.start = true
//   try {
//     const success = await curriculumStore.startReview(props.review.id)
//     if (success) {
//       emit('reviewStart', props.review)
//       // 打开复习页面
//       window.open(`/course?reviewId=${props.review.id}`, '_blank')
//     }
//   } finally {
//     loading.value.start = false
//   }
    loading.value.start = true
    try {
        const success = await curriculumStore.startCourse(props.review.id)
        if (success) {
        emit('courseStart', props.course)
        }
    } finally {
        loading.value.start = false
    }
}


// 备用处理函数（暂时注释）
// const handleCompleteReview = async () => {
//   try {
//     await ElMessageBox.confirm(
//       '确定完成这次复习吗？',
//       '确认完成复习',
//       {
//         confirmButtonText: '确定',
//         cancelButtonText: '取消',
//         type: 'success'
//       }
//     )

//     loading.value.complete = true
//     const success = await curriculumStore.completeReview(props.review.id)
//     if (success) {
//       emit('reviewComplete', props.review)
//     }
//   } catch {
//     // 用户取消操作
//   } finally {
//     loading.value.complete = false
//   }
// }

// const handleViewReview = () => {
//   emit('reviewView', props.review)
//   window.open(`/course?reviewId=${props.review.id}&mode=view`, '_blank')
// }
</script>

<style lang="scss" scoped>
.review-item {
  background: #fff;
  border-radius: 10px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  border-left: 4px solid #e0e0e0;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);
  }

  &.is-overdue {
    border-left-color: #f56c6c;
    background: linear-gradient(135deg, #fef0f0 0%, #ffffff 100%);
  }

  // 不同状态的样式
  &.status-待开始 {
    border-left-color: #e6a23c;
  }

  &.status-进行中 {
    border-left-color: #409eff;
    background: linear-gradient(135deg, #ecf5ff 0%, #ffffff 100%);
  }

  &.status-已完成 {
    border-left-color: #67c23a;
    opacity: 0.9;
  }
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.review-info {
  flex: 1;
}

.student-name {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 6px;
  
  .student-icon {
    color: #3498db;
    margin-right: 6px;
  }
}

.review-meta {
  display: flex;
  gap: 12px;
  font-size: 14px;
  color: #7f8c8d;
  
  .review-type {
    background: #f0f9ff;
    color: #1e40af;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
  }
  
  .word-count {
    background: #f0fdf4;
    color: #166534;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
  }
}

.review-status {
  flex-shrink: 0;
  
  .status-icon {
    margin-right: 4px;
  }
}

.review-time {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  color: #6b7280;
  font-size: 14px;
  
  .time-icon {
    margin-right: 6px;
    color: #9ca3af;
  }
  
  .scheduled-time {
    margin-right: 8px;
  }
  
  .overdue-text {
    color: #ef4444;
    font-weight: 600;
    background: #fef2f2;
    padding: 2px 6px;
    border-radius: 8px;
  }
}

.review-actions {
  display: flex;
  gap: 8px;
  
  .el-button {
    flex: 1;
  }
}

.review-progress {
  margin-top: 12px;
  
  :deep(.el-progress-bar__outer) {
    border-radius: 10px;
  }
  
  :deep(.el-progress-bar__inner) {
    border-radius: 10px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .review-item {
    padding: 12px;
    margin-bottom: 8px; /* 减少移动端的间距 */
  }

  .review-header {
    flex-direction: column;
    gap: 8px;
    margin-bottom: 8px; /* 减少移动端的间距 */
  }

  .review-meta {
    flex-direction: column;
    gap: 4px;
  }

  .review-time {
    margin-bottom: 8px; /* 减少移动端的间距 */
    font-size: 13px; /* 稍微减小字体 */
  }

  .review-actions {
    flex-direction: row; /* 保持水平布局 */
    gap: 6px; /* 减少按钮间距 */

    .el-button {
      flex: 1; /* 按钮等宽分布 */
      font-size: 13px; /* 稍微减小字体 */
      padding: 6px 12px; /* 减少内边距 */
    }
  }

  .student-name {
    font-size: 15px; /* 稍微减小字体 */
    margin-bottom: 4px;
  }
}
</style>