<template>
  <el-dialog
    v-model="visible"
    title="调课"
    width="600px"
    :before-close="handleClose"
    class="reschedule-dialog"
    :close-on-click-modal="false"
  >
    <div class="reschedule-content">
      <!-- 当前课程信息 -->
      <div class="current-course-info">
        <h4 class="section-title">当前课程信息</h4>
        <div class="course-info-grid">
          <div class="info-item">
            <span class="label">学生：</span>
            <span class="value">{{ course.studentName }}</span>
          </div>
          <div class="info-item">
            <span class="label">老师：</span>
            <span class="value">{{ course.teacherName }}</span>
          </div>
          <div class="info-item">
            <span class="label">科目：</span>
            <span class="value">{{ course.subject || '英语' }}</span>
          </div>
          <div class="info-item">
            <span class="label">规格：</span>
            <span class="value">{{ course.specification || '单词课' }}</span>
          </div>
          <div class="info-item">
            <span class="label">当前时间：</span>
            <span class="value">{{ formatDateTime(course.startTime) }}</span>
          </div>
          <div class="info-item">
            <span class="label">时长：</span>
            <span class="value">{{ course.duration }}分钟</span>
          </div>
        </div>
      </div>

      <!-- 调课表单 -->
      <div class="reschedule-form">
        <h4 class="section-title">调课到</h4>
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="80px"
        >
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="日期" prop="date">
                <el-date-picker
                  v-model="form.date"
                  type="date"
                  placeholder="选择日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  :disabled-date="disabledDate"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开始时间" prop="startTime">
                <el-time-picker
                  v-model="form.startTime"
                  format="HH:mm"
                  value-format="HH:mm"
                  placeholder="选择开始时间"
                  style="width: 100%"
                  @change="updateEndTime"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="时长">
                <el-input
                  :value="course.duration + '分钟'"
                  disabled
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结束时间">
                <el-input
                  :value="form.endTime"
                  disabled
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="调课原因" prop="reason">
            <el-input
              v-model="form.reason"
              type="textarea"
              :rows="3"
              placeholder="请说明调课原因..."
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit"
          :loading="submitting"
        >
          确认调课
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useCurriculumStore } from '@/stores/curriculum'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  course: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const curriculumStore = useCurriculumStore()
const formRef = ref()
const submitting = ref(false)

// 表单数据
const form = ref({
  date: '',
  startTime: '',
  endTime: '',
  reason: ''
})

// 表单验证规则
const rules = {
  date: [
    { required: true, message: '请选择日期', trigger: 'change' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请填写调课原因', trigger: 'blur' },
    { min: 5, message: '调课原因至少5个字符', trigger: 'blur' }
  ]
}

// 控制对话框显示
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 禁用过去的日期
const disabledDate = (time) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

// 格式化日期时间
const formatDateTime = (dateTimeString) => {
  if (!dateTimeString) return ''
  const date = new Date(dateTimeString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 更新结束时间
const updateEndTime = () => {
  if (!form.value.startTime || !props.course.duration) {
    form.value.endTime = ''
    return
  }

  const [hours, minutes] = form.value.startTime.split(':').map(Number)
  const startDate = new Date()
  startDate.setHours(hours, minutes, 0, 0)

  const endDate = new Date(startDate.getTime() + props.course.duration * 60 * 1000)
  
  // 检查是否跨越了午夜
  if (endDate.getDate() !== startDate.getDate()) {
    form.value.endTime = ''
    ElMessage.warning('课程时长过长，结束时间不能跨越午夜')
    return
  }

  const endHours = endDate.getHours().toString().padStart(2, '0')
  const endMinutes = endDate.getMinutes().toString().padStart(2, '0')
  form.value.endTime = `${endHours}:${endMinutes}`
}

// 监听开始时间变化
watch(() => form.value.startTime, updateEndTime)

// 提交调课
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    const rescheduleData = {
      courseId: props.course.id,
      newDate: form.value.date,
      newStartTime: form.value.startTime,
      newEndTime: form.value.endTime,
      reason: form.value.reason
    }

    const success = await curriculumStore.rescheduleCourse(rescheduleData)
    
    if (success) {
      ElMessage.success('调课成功')
      emit('success')
      handleClose()
    }
  } catch (error) {
    console.error('调课失败:', error)
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  // 重置表单
  form.value = {
    date: '',
    startTime: '',
    endTime: '',
    reason: ''
  }
  formRef.value?.clearValidate()
}
</script>

<style lang="scss" scoped>
.reschedule-dialog {
  .reschedule-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .section-title {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    border-bottom: 2px solid #409eff;
    padding-bottom: 8px;
  }

  .current-course-info {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #409eff;
  }

  .course-info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .info-item {
    display: flex;
    align-items: center;

    .label {
      font-weight: 500;
      color: #606266;
      min-width: 60px;
    }

    .value {
      color: #303133;
      font-weight: 500;
    }
  }

  .reschedule-form {
    .section-title {
      border-bottom-color: #67c23a;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .reschedule-dialog {
    .course-info-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
