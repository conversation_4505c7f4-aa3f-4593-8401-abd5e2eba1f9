<template>
  <div class="month-view">
    <!-- 月视图头部 -->
    <div class="month-header">
      <div class="month-navigation">
        <el-button 
          :icon="ArrowLeft" 
          @click="goToPrevMonth"
          size="small"
          circle
        />
        <span class="month-title">{{ monthTitle }}</span>
        <el-button 
          :icon="ArrowRight" 
          @click="goToNextMonth"
          size="small"
          circle
        />
        <el-button 
          type="primary" 
          size="small"
          @click="goToToday"
          :disabled="isCurrentMonth"
        >
          本月
        </el-button>
      </div>
    </div>

    <!-- 月视图日历 -->
    <div class="month-calendar">
      <!-- 星期头部 -->
      <div class="week-header">
        <div 
          v-for="day in weekDays" 
          :key="day"
          class="week-day"
        >
          {{ day }}
        </div>
      </div>

      <!-- 日期网格 -->
      <div class="calendar-grid">
        <div
          v-for="date in calendarDates"
          :key="date.key"
          class="calendar-cell"
          :class="{
            'is-other-month': date.isOtherMonth,
            'is-today': date.isToday,
            'is-weekend': date.isWeekend,
            'has-courses': date.courses.length > 0
          }"
          @click="handleDateClick(date)"
        >
          <!-- 日期头部 -->
          <div class="date-header">
            <span class="date-number">{{ date.date }}</span>
            <span v-if="date.courses.length > 0" class="course-count">
              {{ date.courses.length }}
            </span>
          </div>

          <!-- 课程列表 -->
          <div class="date-courses">
            <div
              v-for="course in date.courses.slice(0, maxCoursesPerDay)"
              :key="course.id"
              class="course-item"
              :class="`status-${course.status}`"
              @click.stop="handleCourseClick(course)"
            >
              <div class="course-time">
                {{ formatCourseTime(course) }}
              </div>
              <div class="course-info">
                <span v-if="showStudentName" class="student-name">
                  {{ course.studentName }}
                </span>
                <span v-if="showTeacherName" class="teacher-name">
                  {{ course.teacherName }}
                </span>
              </div>
              <CourseStatusTag :status="course.status" />
            </div>
            
            <!-- 显示更多课程 -->
            <div 
              v-if="date.courses.length > maxCoursesPerDay"
              class="more-courses"
              @click.stop="handleShowMore(date)"
            >
              还有 {{ date.courses.length - maxCoursesPerDay }} 节课...
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 课程详情弹窗 -->
    <el-dialog
      v-model="courseDialogVisible"
      title="课程详情"
      width="500px"
    >
      <CourseCard
        v-if="selectedCourse"
        :course="selectedCourse"
        :show-student-name="showStudentName"
        :show-teacher-name="showTeacherName"
        :show-actions="true"
        @course-start="handleCourseAction"
        @course-end="handleCourseAction"
        @course-cancel="handleCourseAction"
        @course-reschedule="handleReschedule"
      />
    </el-dialog>

    <!-- 日期课程列表弹窗 -->
    <el-dialog
      v-model="dateCoursesDialogVisible"
      :title="`${selectedDateText} 的课程安排`"
      width="600px"
    >
      <div class="date-courses-list">
        <CourseCard
          v-for="course in selectedDateCourses"
          :key="course.id"
          :course="course"
          :show-student-name="showStudentName"
          :show-teacher-name="showTeacherName"
          :show-actions="true"
          @course-click="handleCourseClick"
        />
      </div>
    </el-dialog>

    <!-- 调课弹窗 -->
    <RescheduleDialog
      v-if="rescheduleDialogVisible"
      v-model="rescheduleDialogVisible"
      :course="selectedCourse"
      @success="handleRescheduleSuccess"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import { useCurriculumStore } from '@/stores/curriculum'
import CourseCard from './CourseCard.vue'
import CourseStatusTag from './CourseStatusTag.vue'
import RescheduleDialog from './RescheduleDialog.vue'

const props = defineProps({
  courses: {
    type: Array,
    default: () => []
  },
  showStudentName: {
    type: Boolean,
    default: true
  },
  showTeacherName: {
    type: Boolean,
    default: true
  },
  maxCoursesPerDay: {
    type: Number,
    default: 3
  }
})

const emit = defineEmits(['month-change', 'date-click', 'course-action'])

const curriculumStore = useCurriculumStore()

// 响应式数据
const courseDialogVisible = ref(false)
const rescheduleDialogVisible = ref(false)
const dateCoursesDialogVisible = ref(false)
const selectedCourse = ref(null)
const selectedDateCourses = ref([])
const selectedDateText = ref('')

// 星期头部
const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

// 计算属性
const currentMonth = computed(() => curriculumStore.selectedDate)

const monthTitle = computed(() => {
  const date = currentMonth.value
  return `${date.getFullYear()}年${date.getMonth() + 1}月`
})

const isCurrentMonth = computed(() => {
  const now = new Date()
  const current = currentMonth.value
  return now.getFullYear() === current.getFullYear() && 
         now.getMonth() === current.getMonth()
})

// 生成日历日期
const calendarDates = computed(() => {
  const year = currentMonth.value.getFullYear()
  const month = currentMonth.value.getMonth()
  
  // 获取月份第一天和最后一天
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  
  // 获取第一周的周一和最后一周的周日
  const startDate = new Date(firstDay)
  const dayOfWeek = firstDay.getDay()
  const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1
  startDate.setDate(firstDay.getDate() - daysToSubtract)
  
  const endDate = new Date(lastDay)
  const lastDayOfWeek = lastDay.getDay()
  const daysToAdd = lastDayOfWeek === 0 ? 0 : 7 - lastDayOfWeek
  endDate.setDate(lastDay.getDate() + daysToAdd)
  
  const dates = []
  const today = new Date().toDateString()
  
  for (let current = new Date(startDate); current <= endDate; current.setDate(current.getDate() + 1)) {
    const dateString = current.toDateString()
    const isOtherMonth = current.getMonth() !== month
    const isToday = dateString === today
    const isWeekend = current.getDay() === 0 || current.getDay() === 6
    
    // 获取当天的课程
    const dayCourses = props.courses.filter(course => {
      return new Date(course.startTime).toDateString() === dateString
    })
    
    dates.push({
      key: `${current.getFullYear()}-${current.getMonth()}-${current.getDate()}`,
      date: current.getDate(),
      fullDate: new Date(current),
      dateString: dateString,
      isOtherMonth,
      isToday,
      isWeekend,
      courses: dayCourses
    })
  }
  
  return dates
})

// 方法
const goToPrevMonth = () => {
  const newDate = new Date(currentMonth.value)
  newDate.setMonth(newDate.getMonth() - 1)
  curriculumStore.setSelectedDate(newDate)
  emit('month-change', newDate)
}

const goToNextMonth = () => {
  const newDate = new Date(currentMonth.value)
  newDate.setMonth(newDate.getMonth() + 1)
  curriculumStore.setSelectedDate(newDate)
  emit('month-change', newDate)
}

const goToToday = () => {
  const today = new Date()
  curriculumStore.setSelectedDate(today)
  emit('month-change', today)
}

const formatCourseTime = (course) => {
  const startTime = new Date(course.startTime)
  const endTime = new Date(course.endTime)
  
  return `${startTime.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })}-${endTime.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })}`
}

const handleDateClick = (date) => {
  if (date.courses.length === 0) {
    // 空日期点击，可以触发排课
    emit('date-click', date.dateString)
  } else if (date.courses.length === 1) {
    // 只有一节课，直接显示课程详情
    handleCourseClick(date.courses[0])
  } else {
    // 多节课，显示日期课程列表
    handleShowMore(date)
  }
}

const handleCourseClick = (course) => {
  selectedCourse.value = course
  courseDialogVisible.value = true
}

const handleShowMore = (date) => {
  selectedDateCourses.value = date.courses
  selectedDateText.value = date.fullDate.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
  dateCoursesDialogVisible.value = true
}

const handleCourseAction = (course) => {
  emit('course-action', course)
  courseDialogVisible.value = false
}

const handleReschedule = (course) => {
  courseDialogVisible.value = false
  rescheduleDialogVisible.value = true
}

const handleRescheduleSuccess = () => {
  rescheduleDialogVisible.value = false
  selectedCourse.value = null
  emit('course-action', null) // 触发数据刷新
}

// 监听选中日期变化
watch(currentMonth, () => {
  // 可以在这里触发数据刷新
})
</script>

<style lang="scss" scoped>
.month-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.month-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f8f9fa;
}

.month-navigation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  
  .month-title {
    font-size: 18px;
    font-weight: 600;
    color: #374151;
    min-width: 120px;
    text-align: center;
  }
}

.month-calendar {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.week-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.week-day {
  padding: 12px 8px;
  text-align: center;
  font-weight: 600;
  color: #6b7280;
  border-right: 1px solid #e5e7eb;
  
  &:last-child {
    border-right: none;
  }
}

.calendar-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-template-rows: repeat(6, 1fr);
}

.calendar-cell {
  border-right: 1px solid #e5e7eb;
  border-bottom: 1px solid #e5e7eb;
  padding: 8px;
  cursor: pointer;
  position: relative;
  background: #fff;
  transition: all 0.2s ease;
  
  &:hover {
    background: #f8f9fa;
  }
  
  &.is-other-month {
    background: #f9fafb;
    color: #9ca3af;
  }
  
  &.is-today {
    background: #eff6ff;
    
    .date-number {
      background: #3b82f6;
      color: white;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  
  &.is-weekend {
    background: #fefefe;
  }
  
  &.has-courses {
    background: #f0f9ff;
  }
  
  &:nth-child(7n) {
    border-right: none;
  }
}

.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.date-number {
  font-weight: 600;
  font-size: 14px;
}

.course-count {
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
}

.date-courses {
  max-height: calc(100% - 28px);
  overflow: hidden;
}

.course-item {
  margin-bottom: 2px;
  padding: 2px 4px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  border-left: 3px solid #e5e7eb;
  background: rgba(255, 255, 255, 0.8);
  
  &:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  &.status-待开始 {
    border-left-color: #6b7280;
  }
  
  &.status-进行中 {
    border-left-color: #f59e0b;
  }
  
  &.status-已完成 {
    border-left-color: #10b981;
  }
  
  &.status-cancelled {
    border-left-color: #ef4444;
    opacity: 0.7;
  }
}

.course-time {
  font-weight: 600;
  color: #374151;
  margin-bottom: 1px;
}

.course-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2px;
  
  .student-name, .teacher-name {
    color: #6b7280;
    font-size: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.more-courses {
  color: #3b82f6;
  font-size: 10px;
  text-align: center;
  padding: 2px;
  cursor: pointer;
  
  &:hover {
    background: rgba(59, 130, 246, 0.1);
    border-radius: 2px;
  }
}

.date-courses-list {
  max-height: 500px;
  overflow-y: auto;
  
  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .month-header {
    padding: 12px 16px;
  }
  
  .month-navigation {
    .month-title {
      font-size: 16px;
      min-width: 100px;
    }
  }
  
  .week-day {
    padding: 8px 4px;
    font-size: 12px;
  }
  
  .calendar-cell {
    padding: 4px;
  }
  
  .course-item {
    font-size: 10px;
  }
  
  .course-time {
    font-size: 9px;
  }
}
</style>