import request from '@/utils/request'
import type {
  ApiResponse,
  GetScheduleParams,
  GetReviewPlansParams,
  CancelCourseParams,
  RescheduleCourseParams,
  GetStudentsParams,
  GetTeachersParams,
  PageResponse,
  CourseSchedule,
  ReviewPlan,
  ScheduleRequest,
  Student,
  Teacher,
  TeacherAvailableTimeSlots
} from './types'

/**
 * 获取课表数据
 * @param params 查询参数
 * @returns 课表列表
 */
export function getScheduleApi(params: GetScheduleParams): Promise<ApiResponse<CourseSchedule[]>> {
  return request({
    url: '/curriculum/schedule',
    method: 'get',
    params
  })
}

/**
 * 排课
 * @param data 排课数据
 * @returns 操作结果
 */
export function createScheduleApi(data: ScheduleRequest): Promise<ApiResponse<void>> {
  return request({
    url: '/curriculum/schedule',
    method: 'post',
    data
  })
}

/**
 * 开始上课
 * @param courseId 课程ID
 * @returns 操作结果
 */
export function startCourseApi(courseId: string): Promise<ApiResponse<void>> {
  return request({
    url: `/course/start/${courseId}`,
    method: 'post'
  })
}

/**
 * 停课
 * @param courseId 课程ID
 * @param data 停课数据
 * @returns 操作结果
 */
export function cancelCourseApi(courseId: string, data: CancelCourseParams): Promise<ApiResponse<void>> {
  return request({
    url: `/curriculum/course/${courseId}/cancel`,
    method: 'post',
    data
  })
}

/**
 * 结束上课
 * @param courseId 课程ID
 * @returns 操作结果
 */
export function endCourseApi(courseId: string): Promise<ApiResponse<void>> {
  return request({
    url: `/curriculum/course/${courseId}/end`,
    method: 'post'
  })
}

/**
 * 调课
 * @param data 调课数据
 * @returns 操作结果
 */
export function rescheduleCourseApi(data: RescheduleCourseParams): Promise<ApiResponse<void>> {
  return request({
    url: `/curriculum/course/${data.courseId}/reschedule`,
    method: 'post',
    data
  })
}

/**
 * 获取复习课 - 现在使用统一的课程接口
 * @param params 查询参数
 * @returns 复习课列表
 */
export function getReviewPlansApi(params: GetReviewPlansParams): Promise<ApiResponse<ReviewPlan[]>> {
  // 转换为统一的课程查询参数
  const scheduleParams: GetScheduleParams = {
    teacherId: params.teacherId,
    studentId: params.studentId,
    type: '复习课',  // 指定查询复习课
    viewType: params.viewType,     // 默认使用月视图
    startDate: params.date || new Date().toISOString().split('T')[0],
    endDate: params.viewType === 'today'
      ? params.date || new Date().toISOString().split('T')[0]
      : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 未来30天
  }
  
  console.log('🔧 [修复] getReviewPlansApi 转换参数:', scheduleParams)
  console.log('🔧 [修复] 原始复习参数:', params)
  console.log('🔧 [修复] 现在使用统一的课程接口获取复习课')
  
  return request({
    url: '/curriculum/schedule',  // 使用统一的课程接口
    method: 'get',
    params: scheduleParams
  })
}

/**
 * 开始复习
 * @param reviewId 复习ID
 * @returns 操作结果
 */
export function startReviewApi(reviewId: string): Promise<ApiResponse<void>> {
  return request({
    url: `/curriculum/review/${reviewId}/start`,
    method: 'post'
  })
}

/**
 * 完成复习
 * @param reviewId 复习ID
 * @returns 操作结果
 */
export function completeReviewApi(reviewId: string): Promise<ApiResponse<void>> {
  return request({
    url: `/curriculum/review/${reviewId}/complete`,
    method: 'post'
  })
}

/**
 * 获取学生列表
 * @param params 查询参数
 * @returns 学生列表
 */
export function getStudentsApi(params: GetStudentsParams): Promise<ApiResponse<PageResponse<Student>>> {
  return request({
    url: '/curriculum/students',
    method: 'get',
    params
  })
}

/**
 * 获取教师列表
 * @param params 查询参数
 * @returns 教师列表
 */
export function getTeachersApi(params?: GetTeachersParams): Promise<ApiResponse<Teacher[]>> {
  return request({
    url: '/curriculum/teachers',
    method: 'get',
    params
  })
}

/**
 * 获取教师可排课时间段
 * @param teacherId 教师ID
 * @returns 教师可排课时间段
 */
export function getTeacherAvailableTimeSlotsApi(teacherId: string): Promise<ApiResponse<TeacherAvailableTimeSlots>> {
    const id = teacherId ?? ''
    return request({
    url: `/user/teacher/available-time-slots?id=${id}`,
    method: 'get'
  })
}

// 导出所有API方法
export const curriculumApi = {
  getSchedule: getScheduleApi,
  createSchedule: createScheduleApi,
  startCourse: startCourseApi,
  cancelCourse: cancelCourseApi,
  endCourse: endCourseApi,
  rescheduleCourse: rescheduleCourseApi,
  getReviewPlans: getReviewPlansApi,
  startReview: startReviewApi,
  completeReview: completeReviewApi,
  getStudents: getStudentsApi,
  getTeachers: getTeachersApi,
  getTeacherAvailableTimeSlots: getTeacherAvailableTimeSlotsApi
}

export default curriculumApi