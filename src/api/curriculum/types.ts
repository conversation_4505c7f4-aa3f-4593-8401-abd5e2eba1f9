// 课程数据模型 - 统一支持正常课程和复习课
export interface CourseSchedule {
  id: string
  studentId: string
  studentName: string
  teacherId: string
  teacherName: string
  startTime?: string  // 复习课可选，允许协商时间
  endTime?: string    // 复习课可选，允许协商时间
  scheduledDate?: string  // 复习课使用，协商的上课日期
  duration: number
  status: '待开始' | '进行中' | '已完成' | '停课'
  type: '学习课' | '复习课'  // 课程类型：正常课程或复习课
  subject?: string
  classroom?: string
  cancelReason?: string
  cancelType?: 'teacher' | 'student'
  specification?: string
  // 复习课特有字段
  reviewType?: 'D2' | 'D4' | 'D7' | 'D14' | 'D21'  // 复习类型（仅复习课）
}

// 为了保持向后兼容，保留复习课类型别名
export type ReviewPlan = CourseSchedule

// 排课请求数据模型
export interface ScheduleRequest {
  studentId: string
  teacherId: string
  dateRange: [string, string]
  totalLessons: number
  weeklySchedules: {
    dayOfWeek: number // 0-6, 0为周日
    startTime: string
    endTime: string
    duration?: number
  }[]
  duration: number
  subject: string
  specification: string // 新增规格字段
  type: string
}

// 学生数据模型
export interface Student {
  id: string
  name: string
  phone: string
  avatar?: string
  grade?: string
  status: 'active' | 'inactive'
}

// API响应基础接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 获取课表参数
export interface GetScheduleParams {
  teacherId?: string
  studentId?: string
  startDate: string
  endDate: string
  viewType: 'week' | 'month' | 'today' | 'future'
  type?: '学习课' | '复习课' | 'all'
}

// 获取复习课参数
export interface GetReviewPlansParams {
  teacherId?: string
  studentId?: string
  date?: string
  viewType: 'today' | 'future'
}

// 停课参数
export interface CancelCourseParams {
  reason: string
  type: 'teacher' | 'student'
}

// 调课参数
export interface RescheduleCourseParams {
  courseId: string
  newDate: string
  newStartTime: string
  newEndTime: string
  reason: string
}

// 获取学生列表参数
export interface GetStudentsParams {
  teacherId?: string
  keyword?: string
  pageNum?: number
  pageSize?: number
}

// 分页响应
export interface PageResponse<T> {
  total: number
  rows: T[]
  pageNum: number
  pageSize: number
}

// 课表API接口定义
export interface CurriculumAPI {
  // 获取课表数据
  getSchedule(params: GetScheduleParams): Promise<ApiResponse<CourseSchedule[]>>
  
  // 排课
  createSchedule(data: ScheduleRequest): Promise<ApiResponse<void>>
  
  // 开始上课
  startCourse(courseId: string): Promise<ApiResponse<void>>
  
  // 停课
  cancelCourse(courseId: string, data: CancelCourseParams): Promise<ApiResponse<void>>

  // 调课
  rescheduleCourse(data: RescheduleCourseParams): Promise<ApiResponse<void>>

  // 结束上课
  endCourse(courseId: string): Promise<ApiResponse<void>>
  
  // 获取复习课
  getReviewPlans(params: GetReviewPlansParams): Promise<ApiResponse<ReviewPlan[]>>
  
  // 开始复习
  startReview(reviewId: string): Promise<ApiResponse<void>>
  
  // 完成复习
  completeReview(reviewId: string): Promise<ApiResponse<void>>
  
  // 获取学生列表
  getStudents(params: GetStudentsParams): Promise<ApiResponse<PageResponse<Student>>>
  
  // 获取教师列表
  getTeachers(params: GetTeachersParams): Promise<ApiResponse<Teacher[]>>
  
  // 获取教师可排课时间段
  getTeacherAvailableTimeSlots(teacherId: string): Promise<ApiResponse<TeacherAvailableTimeSlots>>
}

// 教师信息
export interface Teacher {
  id: string
  name: string
  phone: string
  email?: string
  avatar?: string
  status: 'active' | 'inactive'
  createdAt?: string
}

// 时间段
export interface TimeSlot {
  startTime: string
  endTime: string
}

// 可排课时间段
export interface AvailableTimeSlot {
  weekday: number  // 星期几，1-7，1为周一，7为周日
  timeSlots: TimeSlot[]
}

// 教师可排课时间段响应
export interface TeacherAvailableTimeSlots {
  teacherId: string
  teacherName: string
  availableTimeSlots: AvailableTimeSlot[]
}

// 获取教师列表参数
export interface GetTeachersParams {
  keyword?: string
  page?: number
  pageSize?: number
}